import { Test, TestingModule } from '@nestjs/testing';
import { RecipeGeneratorModule } from './recipe-generator.module';
import { RecipeGeneratorController } from './recipe-generator.controller';
import { RecipeGeneratorService } from './recipe-generator.service';

describe('RecipeGeneratorModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [RecipeGeneratorModule],
    }).compile();
  });

  afterEach(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide RecipeGeneratorController', () => {
    const controller = module.get<RecipeGeneratorController>(
      RecipeGeneratorController,
    );
    expect(controller).toBeDefined();
    expect(controller).toBeInstanceOf(RecipeGeneratorController);
  });

  it('should provide RecipeGeneratorService', () => {
    const service = module.get<RecipeGeneratorService>(RecipeGeneratorService);
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(RecipeGeneratorService);
  });

  it('should inject RecipeGeneratorService into RecipeGeneratorController', () => {
    const controller = module.get<RecipeGeneratorController>(
      RecipeGeneratorController,
    );
    const service = module.get<RecipeGeneratorService>(RecipeGeneratorService);

    // Access the private service property through reflection for testing
    const controllerService = (controller as any).recipeGeneratorService;
    expect(controllerService).toBe(service);
  });
});
