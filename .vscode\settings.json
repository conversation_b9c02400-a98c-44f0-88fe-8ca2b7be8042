{
  // Use Prettier as the default formatter
  "editor.defaultFormatter": "esbenp.prettier-vscode",

  // Format files automatically when saving
  "editor.formatOnSave": true,

  // Enable ESLint to also format (fix) problems
  "eslint.format.enable": true,

  // Tell ESLint which file types it should check
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "json"
  ]
}
