import {
  Body,
  Controller,
  Post,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  RecipeGeneratorService,
  RecipeGeneratorInput,
  RecipeGeneratorOutput,
} from './recipe-generator.service';

@Controller('recipe-generator')
export class RecipeGeneratorController {
  constructor(
    private readonly recipeGeneratorService: RecipeGeneratorService,
  ) {}

  @Post('generate')
  async generateRecipe(
    @Body() body: RecipeGeneratorInput,
  ): Promise<RecipeGeneratorOutput> {
    try {
      // Validate required fields
      if (!body.url) {
        throw new HttpException('URL is required', HttpStatus.BAD_REQUEST);
      }

      // Validate URL format (basic TikTok URL validation)
      const tiktokUrlPattern =
        /^https?:\/\/(www\.)?(tiktok\.com|vm\.tiktok\.com)/i;
      if (!tiktokUrlPattern.test(body.url)) {
        throw new HttpException(
          'Invalid TikTok URL format',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.recipeGeneratorService.generateRecipe({
        url: body.url,
        language: body.language || 'en', // Default to English if not specified
      });

      return result;
    } catch (error) {
      // Handle service errors
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error for debugging
      console.error('Recipe generation error:', error);

      // Return a generic error message to the client
      throw new HttpException(
        'Failed to generate recipe. Please check the URL and try again.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
