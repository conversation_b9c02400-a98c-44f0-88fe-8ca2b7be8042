import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Tik<PERSON> from '@tobyg74/tiktok-api-dl';
import z from 'zod';
import {
  AIMessage,
  BaseMessage,
  SystemMessage,
  HumanMessage,
} from '@langchain/core/messages';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { StateGraph, Annotation, START, END } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import * as ffmpeg from 'fluent-ffmpeg';
import { ttdl } from 'btch-downloader';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

export interface RecipeGeneratorInput {
  url: string;
  language?: string;
}

export interface VideoInfo {
  videoPath: string;
  videoDescription: string;
}

export interface ThumbnailExtractionResult {
  imageUrl: string;
  imagePath?: string;
  timestamp: string;
  success: boolean;
  error?: string;
}

export interface RecipeWithThumbnails extends RecipeOutput {
  thumbnail_extractions?: ThumbnailExtractionResult[];
}

// Output type for generateRecipe method
export interface RecipeGeneratorOutput {
  recipe: RecipeOutput;
  imagesUrls: string[];
}

// Type definition for btch-downloader result
interface BTCHDownloaderResult {
  video?: string[];
  title?: string;
}

const ingredientSchema = z.object({
  quantity: z.number(),
  unit: z.string(),
  name: z.string(),
});

const stepSchema = z.object({
  name: z
    .string()
    .describe('Name of the step. Should be a short and descriptive name.'),
  description: z
    .string()
    .describe(
      'Detailed description of the step. Should include all substeps and necessary information to perform the step.',
    ),
});

const metadataSchema = z.object({
  audio_helpful: z.boolean().optional(),
  video_type: z.enum(['cinematic', 'vlog', 'tutorial', 'other']).optional(),
});

type SceneChange = {
  timestamp: string; // Timestamp in MM:SS format (e.g., "02:45")
  frame_number: number; // Frame number where scene change occurs
};

const recipeSchema = z.object({
  title: z.string().min(1).max(200),
  cuisine: z
    .string()
    .optional()
    .describe('Cuisine of the recipe. e.g: Italian, Mexican, Chinese, etc.'),
  category: z
    .string()
    .optional()
    .describe(
      'Category of the recipe. e.g: Main dish, Side dish, Dessert, etc.',
    ),
  description: z
    .string()
    .describe(
      'Short description of the recipe. Should be less than 200 characters.It should be appealing and catchy.',
    ),
  cooking_time_minutes: z.number().nullable().optional(),
  servings: z.number().nullable().optional(),
  ingredients: z.array(ingredientSchema).default([]),
  steps: z
    .array(stepSchema)
    .default([])
    .describe(
      'List of steps to prepare the recipe. Should include all necessary information to perform the step.',
    ),
  metadata: metadataSchema.optional(),
});

export type RecipeOutput = z.infer<typeof recipeSchema>;

@Injectable()
export class RecipeGeneratorService {
  // System prompt for recipe generation
  private readonly systemPrompt = `
You are an expert culinary assistant.
You will receive a cooking video (e.g., TikTok) and the video description.
Extract a clean, structured recipe from both the video content and the video description, as ingredients and steps may be mentioned in either source.
If there is insufficient info, make reasonable assumptions.

Output requirements:
- Keep measurements and quantities if mentioned.
- Keep the order of steps as presented. provide a detailled description of each step.
`;

  private s3Client: S3Client;

  constructor(private readonly configService: ConfigService) {
    this.initializeS3Client();
  }

  private initializeS3Client() {
    const s3Config = this.configService.get('s3');

    this.s3Client = new S3Client({
      endpoint: s3Config.endpoint,
      region: s3Config.region,
      credentials: {
        accessKeyId: s3Config.accessKeyId,
        secretAccessKey: s3Config.secretAccessKey,
      },
      forcePathStyle: true, // Required for S3-compatible services like MinIO
    });
  }

  private async uploadToS3(filePath: string, key: string): Promise<string> {
    const s3Config = this.configService.get('s3');

    try {
      const fileContent = await fs.promises.readFile(filePath);

      const command = new PutObjectCommand({
        Bucket: s3Config.bucket,
        Key: key,
        Body: fileContent,
        ContentType: 'image/jpeg',
        ACL: 'public-read',
      });

      await this.s3Client.send(command);

      // Return the public URL
      if (s3Config.publicUrl) {
        return `${s3Config.publicUrl}/${key}`;
      } else {
        return `${s3Config.endpoint}/${s3Config.bucket}/${key}`;
      }
    } catch (error) {
      console.error('Error uploading to S3:', error);
      throw error;
    }
  }

  private async cleanupLocalFile(filePath: string): Promise<void> {
    try {
      if (filePath && fs.existsSync(filePath)) {
        await fs.promises.unlink(filePath);
        console.log(`Cleaned up local file: ${filePath}`);
      }
    } catch (error) {
      console.warn(`Failed to clean up local file ${filePath}:`, error);
    }
  }

  private selectRandomTimestamps(
    timestamps: string[],
    maxCount: number,
  ): string[] {
    if (timestamps.length <= maxCount) {
      return timestamps;
    }

    // Create a copy to avoid mutating the original array
    const shuffled = [...timestamps];

    // Fisher-Yates shuffle algorithm
    for (let i = shuffled.length - 1; i > 0; i--) {
      const randomIndex = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[randomIndex]] = [
        shuffled[randomIndex],
        shuffled[i],
      ];
    }

    return shuffled.slice(0, maxCount);
  }

  private AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
      reducer: (x, y) => x.concat(y),
      default: () => [new SystemMessage(this.systemPrompt)],
    }),
    input: Annotation<RecipeGeneratorInput>(),
    videoInfo: Annotation<VideoInfo>(),
    output: Annotation<RecipeOutput>(),
    sceneChanges: Annotation<SceneChange[]>(),
    imagesUrls: Annotation<string[]>(),
  });

  private mimeFromExt(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    switch (ext) {
      case '.mp4':
        return 'video/mp4';
      case '.mov':
        return 'video/quicktime';
      case '.webm':
        return 'video/webm';
      default:
        return 'application/octet-stream';
    }
  }

  private tools = [];

  private toolNode = new ToolNode(this.tools);

  private getContentNode = async (state: typeof this.AgentState.State) => {
    const url = state.input.url;
    let videoPath = '';
    let videoDescription = '';

    try {
      // Primary method: Use downloadTikTokVideo
      console.log('Attempting to download video using primary method...');
      const result = await this.downloadTikTokVideoToby74(url);
      videoPath = result.videoPath;
      videoDescription = result.videoDescription || '';
      console.log('Video downloaded successfully:', videoPath);
    } catch (primaryError) {
      const primaryErrorMsg =
        primaryError instanceof Error
          ? primaryError.message
          : String(primaryError);
      console.log('Primary download method failed:', primaryErrorMsg);

      try {
        const fallbackResult = await this.downloadTiktokVideoBTCH(url);
        videoPath = fallbackResult.videoPath;
        videoDescription = fallbackResult.videoDescription;
      } catch (fallbackError) {
        const fallbackErrorMsg =
          fallbackError instanceof Error
            ? fallbackError.message
            : String(fallbackError);
        console.error('Both download methods failed:');
        console.error('Primary error:', primaryErrorMsg);
        console.error('Fallback error:', fallbackErrorMsg);
        throw new Error(`Failed to download video: ${primaryErrorMsg}`);
      }
    }

    return { videoInfo: { videoPath, videoDescription } };
  };

  private shouldContinueNode = (state: typeof this.AgentState.State) => {
    const lastMessage = state.messages[state.messages.length - 1];

    if (lastMessage && !(lastMessage as AIMessage).tool_calls?.length) {
      return 'cleanup';
    }

    return 'action';
  };

  private cleanupNode = async (state: typeof this.AgentState.State) => {
    const videoPath = state.videoInfo?.videoPath;

    await this.cleanupLocalFile(videoPath);

    return {};
  };

  /**
   * Detect scene changes in video using ffmpeg
   */
  private detectSceneChangesNode = async (
    state: typeof this.AgentState.State,
  ) => {
    const videoPath = state.videoInfo?.videoPath;

    if (!videoPath || !fs.existsSync(videoPath)) {
      console.warn('Video path not found for scene change detection');
      return { sceneChanges: [] };
    }

    try {
      console.log('Detecting scene changes in video...');
      const sceneChanges = await this.detectSceneChanges(videoPath);
      console.log(`Detected ${sceneChanges.length} scene changes`);

      return { sceneChanges };
    } catch (error) {
      console.error('Failed to detect scene changes:', error);
      return { sceneChanges: [] };
    }
  };

  private extractThumbnailsNode = async (
    state: typeof this.AgentState.State,
  ) => {
    const videoPath = state.videoInfo?.videoPath;
    const sceneChanges = state.sceneChanges;

    if (videoPath && sceneChanges && sceneChanges.length > 0) {
      try {
        console.log('Extracting thumbnails from scene changes...');

        // Extract first frame
        const firstFrameResult = await this.extractFirstFrameWithS3Upload(
          videoPath,
          'thumbnails',
        );

        // Extract frames at scene changes (max 4 randomly selected)
        const allTimestamps = sceneChanges.map((sc) => sc.timestamp);
        const selectedTimestamps = this.selectRandomTimestamps(
          allTimestamps,
          5,
        );
        const sceneChangeResults =
          await this.extractThumbnailsFromTimestampsWithS3Upload(
            videoPath,
            selectedTimestamps,
            'thumbnails',
          );

        const allResults = [firstFrameResult, ...sceneChangeResults];
        console.log(
          `Extracted and uploaded ${allResults.filter((r) => r.success).length} thumbnails successfully`,
        );

        // Add thumbnail URLs to the output
        const imagesUrls = allResults.map((r) => r.imageUrl);

        return {
          imagesUrls,
        };
      } catch (error) {
        console.warn('Failed to extract thumbnails:', error);
        return {}; // Continue without thumbnails if extraction fails
      }
    }

    return {};
  };

  private agentNode = async (state: typeof this.AgentState.State) => {
    const messages = state.messages;
    console.log('Invoking model with messages:', messages.length);

    // Get the API key from configuration
    const apiKey = this.configService.get<string>('gemini.apiKey');
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY is not configured');
    }

    const gemini = new ChatGoogleGenerativeAI({
      model: 'gemini-2.5-flash-lite',
      temperature: 1,
      maxRetries: 2,
      apiKey: apiKey,
    });

    const geminiWithSchema = gemini.withStructuredOutput(recipeSchema);

    try {
      const response = await geminiWithSchema.invoke(messages);

      return {
        messages: [response],
        output: response,
      };
    } catch (err) {
      console.error('Model invocation error:', JSON.stringify(err, null, 2));
      return { messages: [new AIMessage('error')] };
    }
  };

  private prepareRequestNode = (state: typeof this.AgentState.State) => {
    const videoPath = state.videoInfo.videoPath;
    const videoDescription = state.videoInfo.videoDescription;
    const language = state.input.language || 'en';
    // Read and encode the video file as base64
    const videoData = fs.readFileSync(videoPath);
    const base64Video = videoData.toString('base64');
    const mimeType = this.mimeFromExt(videoPath);

    // Create the human message with multimodal content
    const content: Array<{
      type: string;
      source_type?: string;
      text?: string;
      mime_type?: string;
      data?: string;
    }> = [
      {
        type: 'text',
        text: `Extract a clean, structured and detailed recipe from this cooking video. The language of the returned recipe should be ${language}.`,
      },
      {
        type: 'file',
        mime_type: mimeType,
        source_type: 'base64',
        data: base64Video,
      },
    ];

    // Add video description if available
    if (videoDescription && videoDescription.trim()) {
      content.splice(1, 0, {
        type: 'text',
        text: `Video description (may include ingredients/steps):\n${videoDescription}`,
      });
    }

    const humanMessage = new HumanMessage({
      content: content,
    });

    return {
      messages: [humanMessage],
      videoInfo: state.videoInfo,
    };
  };

  // Getter methods for accessing schemas and prompts
  getRecipeSchema() {
    return recipeSchema;
  }

  getSystemPrompt(): string {
    return this.systemPrompt;
  }

  // Validate recipe data against schema
  validateRecipe(data: unknown): RecipeOutput {
    return recipeSchema.parse(data);
  }

  async downloadTikTokVideoToby74(url: string): Promise<VideoInfo> {
    if (!url) throw new Error('Missing TikTok URL');
    const outDir = path.resolve('downloads');
    await fs.promises.mkdir(outDir, { recursive: true });
    console.log('Download directory:', outDir);

    // Explicitly use version v1 to get TiktokAPIResponse with Content type
    const result = await Tiktok.Downloader(url, { version: 'v1' });
    if (result?.status !== 'success' || !result?.result) {
      throw new Error('TikTok API DL did not return success');
    }

    const videoDescription = result.result.desc ?? '';

    // Type guard to ensure we have video data
    if (!result.result.video || !result.result.video.playAddr) {
      throw new Error('No video data found for this TikTok');
    }

    const playAddr = result.result.video.playAddr;
    if (!Array.isArray(playAddr) || playAddr.length === 0) {
      throw new Error('No video URL found for this TikTok');
    }
    const videoUrl = playAddr[1] || playAddr[0];

    const fileName = `tiktok_video_${Date.now()}.mp4`;
    const videoPath = path.join(outDir, fileName);

    const response = await axios.get(videoUrl, { responseType: 'stream' });
    await new Promise<void>((resolvePromise, reject) => {
      const writer = fs.createWriteStream(videoPath);
      (response.data as NodeJS.ReadableStream).pipe(writer);
      writer.on('finish', () => resolvePromise());
      writer.on('error', reject);
    });

    return { videoPath, videoDescription };
  }

  async downloadTiktokVideoBTCH(url: string): Promise<VideoInfo> {
    let videoPath = '';
    let videoDescription = '';

    console.log('Attempting fallback download method...');
    const btchResult = (await ttdl(url)) as BTCHDownloaderResult;

    if (btchResult && btchResult.video && btchResult.video.length > 0) {
      const videoUrl = btchResult.video[0];
      videoDescription = btchResult.title || '';

      // Download the video from the URL
      const outDir = path.resolve('downloads');
      await fs.promises.mkdir(outDir, { recursive: true });

      const fileName = `tiktok_video_fallback_${Date.now()}.mp4`;
      videoPath = path.join(outDir, fileName);

      const response = await axios.get(videoUrl, { responseType: 'stream' });
      await new Promise<void>((resolvePromise, reject) => {
        const writer = fs.createWriteStream(videoPath);
        (response.data as NodeJS.ReadableStream).pipe(writer);
        writer.on('finish', () => resolvePromise());
        writer.on('error', reject);
      });

      console.log('Fallback video downloaded successfully:', videoPath);
      return { videoPath, videoDescription };
    } else {
      throw new Error('No video URL found in btch-downloader result');
    }
  }

  /**
   * Detect scene changes in a video using ffmpeg
   */
  async detectSceneChanges(
    videoPath: string,
    threshold: number = 0.4,
  ): Promise<SceneChange[]> {
    return new Promise((resolve, reject) => {
      const sceneChanges: SceneChange[] = [];

      ffmpeg(videoPath)
        .videoFilter(`select='gt(scene,${threshold})',showinfo`)
        .format('null')
        .on('stderr', (stderrLine) => {
          // Parse ffmpeg output to extract scene change information
          const match = stderrLine.match(/pts_time:(\d+(?:\.\d+)?)/);
          if (match) {
            const timeInSeconds = parseFloat(match[1]);
            const minutes = Math.floor(timeInSeconds / 60);
            const seconds = Math.floor(timeInSeconds % 60);
            const timestamp = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            sceneChanges.push({
              timestamp,
              frame_number: Math.floor(timeInSeconds * 30), // Assuming 30fps
            });
          }
        })
        .on('end', () => {
          console.log(`Detected ${sceneChanges.length} scene changes`);
          resolve(sceneChanges);
        })
        .on('error', (err) => {
          console.error('Error detecting scene changes:', err);
          reject(err);
        })
        .save('-');
    });
  }

  /**
   * Extract the first frame of a video and upload to S3
   */
  async extractFirstFrameWithS3Upload(
    videoPath: string,
    outputDir: string = 'thumbnails',
  ): Promise<ThumbnailExtractionResult> {
    let outputPath = '';

    try {
      // Ensure output directory exists
      const thumbnailDir = path.resolve(outputDir);
      await fs.promises.mkdir(thumbnailDir, { recursive: true });

      // Generate output filename
      const videoName = path.basename(videoPath, path.extname(videoPath));
      const outputFileName = `${videoName}_first_frame.jpg`;
      outputPath = path.join(thumbnailDir, outputFileName);

      return new Promise<ThumbnailExtractionResult>((resolve) => {
        ffmpeg(videoPath)
          .seekInput(0)
          .frames(1)
          .format('image2')
          .on('end', async () => {
            try {
              console.log(`First frame extracted successfully: ${outputPath}`);

              // Upload to S3
              const s3Key = `recipe-generator/thumbnails/${videoName}/${outputFileName}`;
              const imageUrl = await this.uploadToS3(outputPath, s3Key);

              resolve({
                imageUrl,
                imagePath: outputPath,
                timestamp: '00:00',
                success: true,
              });
            } catch (uploadError) {
              console.error('Error uploading first frame to S3:', uploadError);
              resolve({
                imageUrl: '',
                imagePath: outputPath,
                timestamp: '00:00',
                success: false,
                error:
                  uploadError instanceof Error
                    ? uploadError.message
                    : String(uploadError),
              });
            } finally {
              // Always clean up local file
              this.cleanupLocalFile(outputPath);
            }
          })
          .on('error', (err) => {
            console.error('Error extracting first frame:', err.message);
            // Clean up on error as well
            this.cleanupLocalFile(outputPath);
            resolve({
              imageUrl: '',
              imagePath: '',
              timestamp: '00:00',
              success: false,
              error: err.message,
            });
          })
          .save(outputPath);
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Error in extractFirstFrameWithS3Upload: ${errorMessage}`);
      // Clean up on exception
      this.cleanupLocalFile(outputPath);
      return {
        imageUrl: '',
        imagePath: '',
        timestamp: '00:00',
        success: false,
        error: errorMessage,
      };
    }
  }

  private timestampToSeconds(timestamp: string): number {
    const parts = timestamp.split(':');
    if (parts.length < 2 || parts.length > 3) {
      throw new Error(
        `Invalid timestamp format: ${timestamp}. Expected MM:SS or MM:SS:mm format.`,
      );
    }

    const minutes = parseInt(parts[0], 10);
    const seconds = parseInt(parts[1], 10);
    const milliseconds = parts.length === 3 ? parseInt(parts[2], 10) : 0;

    if (
      isNaN(minutes) ||
      isNaN(seconds) ||
      (parts.length === 3 && isNaN(milliseconds))
    ) {
      throw new Error(
        `Invalid timestamp format: ${timestamp}. Expected numeric values.`,
      );
    }

    return minutes * 60 + seconds + milliseconds / 100;
  }

  /**
   * Extract a single thumbnail image from video at specific timestamp and upload to S3
   */
  async extractThumbnailAtTimestampWithS3Upload(
    videoPath: string,
    timestamp: string,
    outputDir: string = 'thumbnails',
    customSize?: string,
  ): Promise<ThumbnailExtractionResult> {
    let outputPath = '';

    try {
      // Ensure output directory exists
      const thumbnailDir = path.resolve(outputDir);
      await fs.promises.mkdir(thumbnailDir, { recursive: true });

      // Convert timestamp to seconds
      const timeInSeconds = this.timestampToSeconds(timestamp);

      // Generate output filename
      const videoName = path.basename(videoPath, path.extname(videoPath));
      const outputFileName = `${videoName}_${timestamp.replace(':', '-')}.jpg`;
      outputPath = path.join(thumbnailDir, outputFileName);

      return new Promise<ThumbnailExtractionResult>((resolve) => {
        const ffmpegCommand = ffmpeg(videoPath)
          .seekInput(timeInSeconds)
          .frames(1)
          .format('image2');

        // Only apply size if customSize is provided, otherwise use original dimensions
        if (customSize) {
          ffmpegCommand.size(customSize);
        }

        ffmpegCommand
          .on('end', async () => {
            try {
              console.log(`Thumbnail extracted successfully: ${outputPath}`);

              // Upload to S3
              const s3Key = `recipe-generator/thumbnails/${videoName}/${outputFileName}`;
              const imageUrl = await this.uploadToS3(outputPath, s3Key);

              resolve({
                imageUrl,
                imagePath: outputPath,
                timestamp,
                success: true,
              });
            } catch (uploadError) {
              console.error(
                `Error uploading thumbnail at ${timestamp} to S3:`,
                uploadError,
              );
              resolve({
                imageUrl: '',
                imagePath: outputPath,
                timestamp,
                success: false,
                error:
                  uploadError instanceof Error
                    ? uploadError.message
                    : String(uploadError),
              });
            } finally {
              // Always clean up local file
              await this.cleanupLocalFile(outputPath);
            }
          })
          .on('error', (err) => {
            console.error(
              `Error extracting thumbnail at ${timestamp}:`,
              err.message,
            );
            // Clean up on error as well
            this.cleanupLocalFile(outputPath);
            resolve({
              imageUrl: '',
              imagePath: '',
              timestamp,
              success: false,
              error: err.message,
            });
          })
          .save(outputPath);
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(
        `Error in extractThumbnailAtTimestampWithS3Upload: ${errorMessage}`,
      );
      // Clean up on exception
      await this.cleanupLocalFile(outputPath);
      return {
        imageUrl: '',
        imagePath: '',
        timestamp,
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Extract multiple thumbnail images from video at specified timestamps and upload to S3
   */
  async extractThumbnailsFromTimestampsWithS3Upload(
    videoPath: string,
    timestamps: string[],
    outputDir: string = 'thumbnails',
    customSize?: string,
  ): Promise<ThumbnailExtractionResult[]> {
    console.log(
      `Extracting ${timestamps.length} thumbnails from video: ${videoPath}`,
    );

    const results: ThumbnailExtractionResult[] = [];

    // Process thumbnails sequentially to avoid overwhelming the system
    for (const timestamp of timestamps) {
      const result = await this.extractThumbnailAtTimestampWithS3Upload(
        videoPath,
        timestamp,
        outputDir,
        customSize,
      );
      results.push(result);
    }

    const successCount = results.filter((r) => r.success).length;
    console.log(
      `Successfully extracted and uploaded ${successCount}/${timestamps.length} thumbnails`,
    );

    return results;
  }

  async generateRecipe({
    url,
    language,
  }: RecipeGeneratorInput): Promise<RecipeGeneratorOutput> {
    const workflow = new StateGraph(this.AgentState)
      .addNode('getContent', this.getContentNode)
      .addNode('prepareRequest', this.prepareRequestNode)
      .addNode('agent', this.agentNode)
      .addNode('action', this.toolNode)
      .addNode('extractSceneChanges', this.detectSceneChangesNode)
      .addNode('extractThumbnails', this.extractThumbnailsNode)
      .addNode('cleanup', this.cleanupNode)
      .addEdge(START, 'getContent')
      .addEdge('getContent', 'prepareRequest')
      .addEdge('prepareRequest', 'agent')
      .addEdge('getContent', 'extractSceneChanges')
      .addEdge('extractSceneChanges', 'extractThumbnails')
      .addEdge('extractThumbnails', 'cleanup')
      .addEdge('cleanup', END)
      .addConditionalEdges('agent', this.shouldContinueNode);

    const agent = workflow.compile();

    try {
      const result = await agent.invoke({
        messages: [],
        input: {
          url,
          language,
        },
        videoInfo: { videoPath: '', videoDescription: '' },
        output: undefined,
        sceneChanges: [],
        imagesUrls: [],
      });

      return {
        recipe: result.output,
        imagesUrls: result.imagesUrls,
      };
    } catch (err) {
      console.error('Error generating recipe:', err);
      throw err;
    }
  }
}
